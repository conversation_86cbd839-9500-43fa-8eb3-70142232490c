# 实施计划

- [ ] 1. 修改 generate_usage_doc 函数中的项目名称获取方式
  - 在 `sca-consumer-agent-copyright/agent/node/generate_usage_doc_node.py` 文件中找到 `project_name = state.project_name` 这一行
  - 将其替换为 `project_name = state.requirement_outline["project_name"]`
  - 确保修改后的代码能够正确从需求大纲中提取项目名称
  - _需求: 1.1, 1.2, 1.3_

- [ ] 2. 验证修改后的功能
  - 创建测试用例验证项目名称能够正确从 requirement_outline 中提取
  - 测试生成的使用文档是否包含正确的项目名称
  - 确保修改不会影响其他功能的正常运行
  - _需求: 1.2, 1.3_