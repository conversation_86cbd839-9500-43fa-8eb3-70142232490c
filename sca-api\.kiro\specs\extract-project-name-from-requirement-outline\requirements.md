# 需求文档

## 介绍

此功能修改 `generate_usage_doc_node.py` 中的项目名称获取方式，从需求大纲中提取项目名称，而不是使用 `state.project_name`。目前，`generate_usage_doc` 函数使用 `state.project_name`，但应该从 `state.requirement_outline` 中提取更有意义的项目名称。

## 需求

### 需求 1

**用户故事：** 作为使用软件著作权代理的开发者，我希望在生成使用文档时从需求大纲中提取项目名称，以便使用文档显示正确的项目名称。

#### 验收标准

1. 当 generate_usage_doc 函数执行时，系统应该从 state.requirement_outline["project_name"] 中提取项目名称，而不是使用 state.project_name
2. 当提取的项目名称用于文档生成时，生成的使用文档应该显示从需求大纲中获取的项目名称
3. 当项目名称被提取后，它应该被用于软件名称的设置和文档标题的生成

